import React, { memo, useCallback } from 'react';
import {
	TextInput as NativeTextInput,
	Platform,
	TouchableOpacity,
	View,
	ViewStyle,
	Text,
	TextInputProps,
	StyleSheet,
} from 'react-native';

interface CustomInput {
	clearButtonMode?: string;
	clearButtonStyle?: ViewStyle | null | undefined;
}

type InputProps = TextInputProps & CustomInput;

const TextInputWithClearBtn = (props: InputProps) => {
	const onClear = useCallback(() => {
		if (props.onChangeText) {
			props.onChangeText('');
		}
	}, []); // eslint-disable-line react-hooks/exhaustive-deps
	return (
		<View style={styles.wrap}>
			<NativeTextInput {...props} />
			{props.clearButtonMode && (
				<ClearButton
					mode={props.clearButtonMode}
					onClear={onClear}
					style={props.clearButtonStyle}
				/>
			)}
		</View>
	);
};

type ClearButtonProps = {
	mode: string;
	onClear?(params?: any): void;
	style?: ViewStyle | ViewStyle[] | null;
};

const ClearButton = memo(({ mode, onClear, style }: ClearButtonProps) => {
	if (mode !== 'never') {
		return (
			<TouchableOpacity style={[styles.clearButon, style]} onPress={onClear}>
				<Text style={styles.clearButonLabel}>✕</Text>
			</TouchableOpacity>
		);
	}
	return null;
});

ClearButton.displayName = 'ClearButton';

export const TextInput =
	Platform.OS === 'android' || Platform.OS === 'web'
		? TextInputWithClearBtn
		: NativeTextInput;

const styles = StyleSheet.create({
	wrap: {
		position: 'relative',
	},
	clearButon: {
		position: 'absolute',
		right: 0,
		top: 0,
		bottom: 0,
		justifyContent: 'center',
		borderWidth: 0,
		paddingHorizontal: 5,
		width: 'auto',
		backgroundColor: 'transparent',
	},
	clearButonLabel: {
		fontSize: 20,
	},
});
